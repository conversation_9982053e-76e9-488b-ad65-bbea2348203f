'use client';

import { useEffect, useRef } from 'react';

const ThreeDBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 3D geometric shapes data
    const shapes = [
      // Large cube (brown/orange)
      {
        type: 'cube',
        x: window.innerWidth * 0.7,
        y: window.innerHeight * 0.3,
        size: 120,
        rotation: 0,
        color: '#D97706',
        rotationSpeed: 0.01
      },
      // Medium cube (darker brown)
      {
        type: 'cube',
        x: window.innerWidth * 0.2,
        y: window.innerHeight * 0.6,
        size: 80,
        rotation: 0,
        color: '#92400E',
        rotationSpeed: 0.015
      },
      // Sphere (orange)
      {
        type: 'sphere',
        x: window.innerWidth * 0.8,
        y: window.innerHeight * 0.7,
        size: 60,
        rotation: 0,
        color: '#F59E0B',
        rotationSpeed: 0.02
      },
      // Small cube
      {
        type: 'cube',
        x: window.innerWidth * 0.1,
        y: window.innerHeight * 0.2,
        size: 50,
        rotation: 0,
        color: '#B45309',
        rotationSpeed: 0.025
      }
    ];

    // Animation function
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      shapes.forEach(shape => {
        ctx.save();
        ctx.translate(shape.x, shape.y);
        ctx.rotate(shape.rotation);

        if (shape.type === 'cube') {
          // Draw 3D cube effect
          const size = shape.size;
          
          // Front face
          ctx.fillStyle = shape.color;
          ctx.fillRect(-size/2, -size/2, size, size);
          
          // Top face (lighter)
          ctx.fillStyle = lightenColor(shape.color, 20);
          ctx.beginPath();
          ctx.moveTo(-size/2, -size/2);
          ctx.lineTo(-size/2 + size/4, -size/2 - size/4);
          ctx.lineTo(size/2 + size/4, -size/2 - size/4);
          ctx.lineTo(size/2, -size/2);
          ctx.closePath();
          ctx.fill();
          
          // Right face (darker)
          ctx.fillStyle = darkenColor(shape.color, 20);
          ctx.beginPath();
          ctx.moveTo(size/2, -size/2);
          ctx.lineTo(size/2 + size/4, -size/2 - size/4);
          ctx.lineTo(size/2 + size/4, size/2 - size/4);
          ctx.lineTo(size/2, size/2);
          ctx.closePath();
          ctx.fill();
          
        } else if (shape.type === 'sphere') {
          // Draw sphere with gradient
          const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, shape.size/2);
          gradient.addColorStop(0, lightenColor(shape.color, 30));
          gradient.addColorStop(1, darkenColor(shape.color, 20));
          
          ctx.fillStyle = gradient;
          ctx.beginPath();
          ctx.arc(0, 0, shape.size/2, 0, Math.PI * 2);
          ctx.fill();
        }

        ctx.restore();

        // Update rotation
        shape.rotation += shape.rotationSpeed;
      });

      requestAnimationFrame(animate);
    };

    animate();

    // Helper functions for color manipulation
    function lightenColor(color: string, percent: number): string {
      const num = parseInt(color.replace("#", ""), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) + amt;
      const G = (num >> 8 & 0x00FF) + amt;
      const B = (num & 0x0000FF) + amt;
      return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    function darkenColor(color: string, percent: number): string {
      const num = parseInt(color.replace("#", ""), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) - amt;
      const G = (num >> 8 & 0x00FF) - amt;
      const B = (num & 0x0000FF) - amt;
      return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
        (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
        (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }

    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

export default ThreeDBackground;
