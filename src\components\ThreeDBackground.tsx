'use client';

import { useEffect, useRef } from 'react';

const ThreeDBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 3D geometric shapes data - matching reference site positioning
    const shapes = [
      // Large main cube (brown/orange) - center-right
      {
        type: 'cube',
        x: window.innerWidth * 0.65,
        y: window.innerHeight * 0.35,
        size: 150,
        rotation: 0.3,
        color: '#D97706',
        rotationSpeed: 0.008
      },
      // Medium cube (darker brown) - upper area
      {
        type: 'cube',
        x: window.innerWidth * 0.75,
        y: window.innerHeight * 0.25,
        size: 100,
        rotation: 0.8,
        color: '#92400E',
        rotationSpeed: 0.012
      },
      // Sphere (bright orange) - right side
      {
        type: 'sphere',
        x: window.innerWidth * 0.8,
        y: window.innerHeight * 0.15,
        size: 80,
        rotation: 0,
        color: '#F59E0B',
        rotationSpeed: 0.015
      },
      // Small accent cube - left side
      {
        type: 'cube',
        x: window.innerWidth * 0.15,
        y: window.innerHeight * 0.25,
        size: 60,
        rotation: 1.2,
        color: '#B45309',
        rotationSpeed: 0.02
      },
      // Additional geometric element - bottom right
      {
        type: 'cube',
        x: window.innerWidth * 0.85,
        y: window.innerHeight * 0.6,
        size: 70,
        rotation: 0.5,
        color: '#EA580C',
        rotationSpeed: 0.01
      }
    ];

    // Animation function
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      shapes.forEach(shape => {
        ctx.save();
        ctx.translate(shape.x, shape.y);
        ctx.rotate(shape.rotation);

        if (shape.type === 'cube') {
          // Draw enhanced 3D cube effect
          const size = shape.size;

          // Add shadow for depth
          ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
          ctx.shadowBlur = 15;
          ctx.shadowOffsetX = 5;
          ctx.shadowOffsetY = 5;

          // Front face
          ctx.fillStyle = shape.color;
          ctx.fillRect(-size/2, -size/2, size, size);

          // Reset shadow for other faces
          ctx.shadowColor = 'transparent';

          // Top face (lighter)
          ctx.fillStyle = lightenColor(shape.color, 30);
          ctx.beginPath();
          ctx.moveTo(-size/2, -size/2);
          ctx.lineTo(-size/2 + size/3, -size/2 - size/3);
          ctx.lineTo(size/2 + size/3, -size/2 - size/3);
          ctx.lineTo(size/2, -size/2);
          ctx.closePath();
          ctx.fill();

          // Right face (darker)
          ctx.fillStyle = darkenColor(shape.color, 25);
          ctx.beginPath();
          ctx.moveTo(size/2, -size/2);
          ctx.lineTo(size/2 + size/3, -size/2 - size/3);
          ctx.lineTo(size/2 + size/3, size/2 - size/3);
          ctx.lineTo(size/2, size/2);
          ctx.closePath();
          ctx.fill();

          // Add edge highlights
          ctx.strokeStyle = lightenColor(shape.color, 40);
          ctx.lineWidth = 2;
          ctx.strokeRect(-size/2, -size/2, size, size);

        } else if (shape.type === 'sphere') {
          // Draw enhanced sphere with gradient and glow
          ctx.shadowColor = shape.color;
          ctx.shadowBlur = 20;

          const gradient = ctx.createRadialGradient(-shape.size/4, -shape.size/4, 0, 0, 0, shape.size/2);
          gradient.addColorStop(0, lightenColor(shape.color, 40));
          gradient.addColorStop(0.7, shape.color);
          gradient.addColorStop(1, darkenColor(shape.color, 30));

          ctx.fillStyle = gradient;
          ctx.beginPath();
          ctx.arc(0, 0, shape.size/2, 0, Math.PI * 2);
          ctx.fill();

          // Add highlight
          ctx.shadowColor = 'transparent';
          ctx.fillStyle = lightenColor(shape.color, 60);
          ctx.beginPath();
          ctx.arc(-shape.size/6, -shape.size/6, shape.size/8, 0, Math.PI * 2);
          ctx.fill();
        }

        ctx.restore();

        // Update rotation
        shape.rotation += shape.rotationSpeed;
      });

      requestAnimationFrame(animate);
    };

    animate();

    // Helper functions for color manipulation
    function lightenColor(color: string, percent: number): string {
      const num = parseInt(color.replace("#", ""), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) + amt;
      const G = (num >> 8 & 0x00FF) + amt;
      const B = (num & 0x0000FF) + amt;
      return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    function darkenColor(color: string, percent: number): string {
      const num = parseInt(color.replace("#", ""), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) - amt;
      const G = (num >> 8 & 0x00FF) - amt;
      const B = (num & 0x0000FF) - amt;
      return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
        (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
        (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }

    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

export default ThreeDBackground;
