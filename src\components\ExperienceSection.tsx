'use client';

import { useState } from 'react';
import Image from 'next/image';

const ExperienceSection = () => {
  const [expandedItems, setExpandedItems] = useState<number[]>([]);

  const toggleExpanded = (index: number) => {
    setExpandedItems(prev =>
      prev.includes(index)
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const experiences = [
    {
      id: 1,
      position: 'Co-Founder',
      company: 'Life Coach Elevate',
      duration: '2024 - Present',
      location: 'Arizona, USA',
      website: 'https://lifecoachelevate.com',
      logo: '/assets/companies/life-coach-elevate.png',
      description: 'Leading the technical development and DevOps infrastructure for a life coaching platform. Implementing scalable solutions using modern cloud technologies and CI/CD pipelines.',
      technologies: ['DevOps', 'CI/CD', 'Kubernetes', 'JavaScript', 'TypeScript', 'NextJS']
    },
    {
      id: 2,
      position: 'Senior Lead Software Engineer',
      company: 'Saimon Global Ltd',
      duration: '2019 - 2024',
      location: 'Dhaka, Bangladesh',
      website: 'https://saimonglobal.com',
      logo: '/assets/companies/saimon-global.png',
      description: 'Led a team of developers in building scalable web and mobile applications. Specialized in React, NextJS, and Flutter development for enterprise clients worldwide.',
      technologies: ['JavaScript', 'TypeScript', 'Dart', 'React', 'NextJS', 'Flutter']
    },
    {
      id: 3,
      position: 'Web Developer',
      company: 'influenceTHIS Canada',
      duration: '2018-2019',
      location: 'Remote (Toronto, Canada)',
      website: 'https://influencethis.ca',
      logo: '/assets/companies/influence-this.png',
      description: 'Developed responsive web applications and marketing websites for Canadian businesses. Focused on performance optimization and modern web technologies.',
      technologies: ['JavaScript', 'GULP', 'SCSS', 'Node.js']
    },
    {
      id: 4,
      position: 'Top Rated Web Developer',
      company: 'Upwork Inc.',
      duration: '2017 - Present',
      location: 'Remote',
      website: 'https://upwork.com',
      logo: '/assets/companies/upwork.png',
      description: 'Maintained a 100% success rate while working with 50+ clients worldwide. Delivered 140+ projects ranging from simple websites to complex web applications.',
      technologies: ['JavaScript', 'PHP', 'HTML', 'CSS', 'Figma']
    }
  ];

  return (
    <section id="experience" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header - Exact Typography Match */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight">
            Professional Experience
          </h2>
        </div>

        {/* Experience Cards - Exact Reference Site Match */}
        <div className="space-y-8 max-w-5xl mx-auto">
          {experiences.map((exp, index) => (
            <div
              key={exp.id}
              className="bg-gray-50 rounded-xl p-8 hover:shadow-lg transition-all duration-300 border border-gray-100"
            >
              <div className="flex items-start gap-6">
                {/* Company Logo */}
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-white rounded-xl overflow-hidden shadow-sm border border-gray-200">
                    <Image
                      src={exp.logo}
                      alt={`${exp.company} logo`}
                      width={64}
                      height={64}
                      className="object-cover w-full h-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                  </div>
                </div>

                {/* Experience Details */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-gray-900 font-semibold text-xl mb-2 leading-tight">
                        {exp.position} @ {exp.company}
                      </h3>
                      <p className="text-gray-600 text-base mb-4 font-medium">{exp.duration}</p>
                      <div className="flex items-center gap-6 text-sm text-gray-500 mb-6">
                        <div className="flex items-center gap-2">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                          <span>{exp.location}</span>
                        </div>
                        <a
                          href={exp.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-500 hover:text-gray-700 transition-colors flex items-center gap-2"
                        >
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                          </svg>
                        </a>
                      </div>
                    </div>
                    {/* Expand/Collapse Button */}
                    <button
                      onClick={() => toggleExpanded(index)}
                      className="text-gray-400 hover:text-gray-600 transition-colors p-2"
                    >
                      <span className="text-2xl font-light">
                        {expandedItems.includes(index) ? '−' : '+'}
                      </span>
                    </button>
                  </div>

                  {/* Description - Collapsible */}
                  {expandedItems.includes(index) && (
                    <div className="mb-6">
                      <p className="text-gray-600 mb-6 leading-relaxed text-base">
                        {exp.description}
                      </p>
                    </div>
                  )}

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-3">
                    {exp.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ExperienceSection;
