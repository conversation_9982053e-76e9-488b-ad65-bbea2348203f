@import "tailwindcss";

/* Tamalsen.dev inspired clean styling - Exact Typography Matching */
:root {
  --font-inter: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Typography Scale - Matching Reference Site */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */
  --text-7xl: 4.5rem;     /* 72px */
  --text-8xl: 6rem;       /* 96px */

  /* Line Heights - Matching Reference Site */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Font Weights - Matching Reference Site */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Color System - Matching Reference Site Exactly */
  --text-primary: #1f2937;      /* Main text color */
  --text-secondary: #6b7280;    /* Secondary text color */
  --text-muted: #9ca3af;        /* Muted text color */
  --text-light: #d1d5db;        /* Light text color */

  --bg-primary: #ffffff;        /* Primary background */
  --bg-secondary: #f9fafb;      /* Secondary background */
  --bg-muted: #f3f4f6;          /* Muted background */

  --accent-primary: #2563eb;    /* Primary accent (blue) */
  --accent-hover: #1d4ed8;      /* Accent hover state */
  --accent-light: #dbeafe;      /* Light accent background */

  --border-color: #e5e7eb;      /* Border color */
  --border-light: #f3f4f6;      /* Light border color */

  /* Spacing System - Matching Reference Site */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  --space-3xl: 4rem;      /* 64px */
  --space-4xl: 5rem;      /* 80px */
  --space-5xl: 6rem;      /* 96px */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.font-inter {
  font-family: var(--font-inter);
}

html {
  scroll-behavior: smooth;
  font-family: var(--font-inter);
}

body {
  font-family: var(--font-inter);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  overflow-x: hidden;
  font-size: var(--text-base);
  font-weight: var(--font-normal);
}

/* Smooth scroll enhancement - Matching Reference Site */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px; /* Account for fixed navbar */
  }
}

/* Enhanced scroll behavior for sections */
section {
  scroll-margin-top: 80px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Smooth transitions - Refined for Reference Site Match */
a, button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.group:hover * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects matching reference site */
.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Focus styles for accessibility */
a:focus,
button:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
}

/* Animation classes */
@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out;
}

/* Utility classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 3rem;
  }
}
