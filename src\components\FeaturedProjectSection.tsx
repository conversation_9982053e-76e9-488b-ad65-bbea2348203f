'use client';

import Link from 'next/link';

const FeaturedProjectSection = () => {
  return (
    <section id="work" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Work Description - Matching Reference Site */}
        <div className="text-center mb-20">
          <p className="text-gray-600 text-lg max-w-4xl mx-auto leading-relaxed mb-16">
            Deployed scalable travel, event and telemedicine web and hybrid mobile apps using React SPA and PWA.
            <br />
            Collaborated in 140+ projects with 50+ clients all around the world. I am also interested in data analytics and visualization.
          </p>
        </div>

        {/* Featured Project - Matching Reference Site */}
        <div className="bg-white rounded-xl p-12 shadow-sm border border-gray-100 text-center">
          <h4 className="text-lg font-medium text-gray-600 mb-4">Featured Project</h4>
          <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8">Tryotel App</h3>
          <Link
            href="/project/tryotel-cross-platform-travel-app"
            className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            View Project
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjectSection;
