'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

const WorkSection = () => {
  const [activeFilter, setActiveFilter] = useState('All');

  const projects = [
    {
      id: 1,
      title: 'Flight Local',
      description: 'B2B Travel Solution',
      category: 'Web Development',
      image: 'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=800&h=600&fit=crop&crop=center',
      url: '/project/flight-local',
      liveUrl: 'https://flightlocal.com'
    },
    {
      id: 2,
      title: 'AI Lab Granada',
      description: 'AI Research Platform',
      category: 'Web Development',
      image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=600&fit=crop&crop=center',
      url: '/project/ai-lab-granada',
      liveUrl: 'https://ailabgranada.com'
    },
    {
      id: 3,
      title: 'Tryotel – Cross-Platform Travel App',
      description: 'Mobile travel booking application',
      category: 'Web Development',
      image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop&crop=center',
      url: '/project/tryotel-cross-platform-travel-app',
      liveUrl: 'https://tryo.tel/app'
    },
    {
      id: 4,
      title: 'Khora – Urban Thinkers Consulting Firm',
      description: 'Consulting firm website',
      category: 'Web Development',
      image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center',
      url: '/project/khora-urban-thinkers',
      liveUrl: 'https://khora.com'
    },
    {
      id: 5,
      title: 'Tapy – Download. Connect. Unlock.',
      description: 'Mobile app platform',
      category: 'Web Development',
      image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=600&fit=crop&crop=center',
      url: '/project/tapy',
      liveUrl: 'https://tapy.app'
    },
    {
      id: 6,
      title: 'Walker IP Pty Ltd',
      description: 'Intellectual property services',
      category: 'Web Development',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop&crop=center',
      url: '/project/walker-ip',
      liveUrl: 'https://walkerip.com.au'
    },
    {
      id: 7,
      title: 'Tryotel Web (B2C)',
      description: 'Consumer travel booking platform',
      category: 'Web Development',
      image: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800&h=600&fit=crop&crop=center',
      url: '/project/tryotel-web',
      liveUrl: 'https://tryotel.com'
    },
    {
      id: 8,
      title: 'Kananaskis Nordic Spa Website',
      description: 'Spa and wellness website',
      category: 'Web Development',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center',
      url: '/project/kananaskis-nordic-spa',
      liveUrl: 'https://kananaskisnordicspa.com'
    },
    {
      id: 9,
      title: 'A Higher Thought',
      description: 'Wellness and mindfulness platform',
      category: 'Web Development',
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center',
      url: '/project/a-higher-thought',
      liveUrl: 'https://ahigherthought.com'
    },
    {
      id: 10,
      title: 'All the roads of Chittagong',
      description: 'Data visualization project',
      category: 'Data Visualization',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop&crop=center',
      url: '/project/chittagong-roads',
      liveUrl: 'https://chittagongroads.com'
    }
  ];

  const categories = [
    { name: 'All', count: 10 },
    { name: 'Data Visualization', count: 1 },
    { name: 'Web Development', count: 8 }
  ];

  const filteredProjects = activeFilter === 'All'
    ? projects
    : projects.filter(project => project.category === activeFilter);

  return (
    <section id="work" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header - Exact Typography Match */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight">
            My Work
          </h2>
          <p className="text-gray-600 text-lg max-w-4xl mx-auto leading-relaxed mb-12">
            Deployed scalable travel, event and telemedicine web and hybrid mobile apps using React SPA and PWA.
            <br />
            Collaborated in 140+ projects with 50+ clients all around the world. I am also interested in data analytics and visualization.
          </p>
        </div>



        {/* Filter Buttons - Exact Reference Site Match */}
        <div className="mb-16">
          <div className="flex flex-wrap items-center justify-center gap-2 text-sm">
            <span className="text-gray-600 font-medium mr-4">Filter by</span>
            {categories.map((category, index) => (
              <React.Fragment key={category.name}>
                <button
                  onClick={() => setActiveFilter(category.name)}
                  className={`px-0 py-1 transition-all duration-300 border-b-2 ${
                    activeFilter === category.name
                      ? 'text-gray-900 border-gray-900 font-medium'
                      : 'text-gray-500 border-transparent hover:text-gray-700'
                  }`}
                >
                  {category.name} {category.count.toString().padStart(2, '0')}
                </button>
                {index < categories.length - 1 && (
                  <span className="text-gray-400 mx-2">/</span>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Projects Grid - Exact Reference Site Match */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg transition-all duration-300 group border border-gray-100"
            >
              {/* Project Image */}
              <div className="aspect-video bg-gray-200 relative overflow-hidden">
                <Image
                  src={project.image}
                  alt={project.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Link
                    href={project.url}
                    className="bg-white text-gray-900 px-4 py-2 rounded-md font-medium hover:bg-gray-100 transition-colors"
                  >
                    Show project
                  </Link>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {project.title}
                  </h3>
                  <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full whitespace-nowrap ml-2">
                    {project.category}
                  </span>
                </div>

                <p className="text-gray-600 text-sm mb-4">
                  {project.description}
                </p>

                <Link
                  href={project.url}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
                >
                  Show project →
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WorkSection;
