'use client';

import Image from 'next/image';

const ContactSection = () => {
  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      position: 'Founder at influenceTHIS Canada',
      company: 'influenceTHIS Canada',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      testimonial: '<PERSON><PERSON> is an exceptional developer who consistently delivers high-quality work. His attention to detail and technical expertise made our project a huge success.'
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON>',
      position: 'Agile Coach | Speaker | Trainer',
      company: 'Independent',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      testimonial: 'Working with <PERSON><PERSON> was a pleasure. His professionalism and ability to understand complex requirements quickly made him an invaluable team member.'
    },
    {
      id: 3,
      name: '<PERSON>',
      position: 'CEO & Founder at The Cliff',
      company: 'The Cliff',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face',
      testimonial: '<PERSON><PERSON> delivered beyond our expectations. His technical skills combined with excellent communication made our collaboration seamless and productive.'
    }
  ];
  return (
    <section id="contact" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Testimonials Section - Exact Reference Site Match */}
        <div className="mb-24">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            {testimonials.map((testimonial) => (
              <div
                key={testimonial.id}
                className="bg-white rounded-xl p-8 hover:shadow-lg transition-shadow duration-300 border border-gray-100"
              >
                <div className="flex items-center mb-6">
                  <Image
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    width={56}
                    height={56}
                    className="rounded-full mr-4"
                  />
                  <div>
                    <h4 className="font-semibold text-gray-900 text-lg">{testimonial.name}</h4>
                    <p className="text-sm text-gray-600 font-medium">{testimonial.position}</p>
                    <p className="text-xs text-gray-500">{testimonial.company}</p>
                  </div>
                </div>
                <p className="text-gray-700 text-base leading-relaxed italic">
                  "{testimonial.testimonial}"
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Section - Exact Reference Site Match */}
        <div className="text-center bg-white rounded-xl p-12">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight">
            Available for select freelance opportunities
          </h2>

          <p className="text-lg text-gray-600 mb-16 leading-relaxed max-w-3xl mx-auto">
            Have an exciting project you need help with?<br />
            Send me an email or contact me via instant message!
          </p>

          {/* Email */}
          <div className="mb-16">
            <a
              href="mailto:<EMAIL>"
              className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 hover:text-blue-600 transition-colors"
            >
              <EMAIL>
            </a>
          </div>

          {/* Social Links */}
          <div className="flex flex-wrap justify-center gap-12 text-gray-600">
            <a
              href="https://m.me/tamalsen"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 transition-colors underline text-lg"
            >
              Messenger
            </a>

            <a
              href="https://www.linkedin.com/in/tamalsen"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 transition-colors underline text-lg"
            >
              LinkedIn
            </a>

            <a
              href="https://www.instagram.com/tamalsen"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 transition-colors underline text-lg"
            >
              Instagram
            </a>

            <a
              href="https://github.com/tamalsen"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 transition-colors underline text-lg"
            >
              Github
            </a>
          </div>
        </div>

        {/* Footer - Exact Reference Site Match */}
        <div className="mt-24 pt-12 border-t border-gray-200 text-center">
          <p className="text-base text-gray-500">
            © 2021. Made with passion by{' '}
            <a href="#" className="text-blue-600 hover:text-blue-700 font-medium">
              Tamal Sen
            </a>
            . All right reserved.
          </p>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
