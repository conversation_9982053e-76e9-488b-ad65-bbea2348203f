'use client';

import Image from 'next/image';

const ExpertiseSection = () => {
  const expertiseAreas = [
    {
      title: 'Software Development',
      description: 'Experienced in both functional and OOP: Dart, Python, Java, JavaScript, TypeScript.',
      icon: '/assets/icons/code-icon.svg',
      technologies: ['Dart', 'Python', 'Java', 'JavaScript', 'TypeScript']
    },
    {
      title: 'Frontend Dev React, NextJS',
      description: 'Passionate about UI/UX. Over 5 years of development experience in HTML, CSS, JS, React and NextJS frameworks.',
      icon: '/assets/icons/react-icon.svg',
      technologies: ['HTML', 'CSS', 'JavaScript', 'React', 'NextJS']
    },
    {
      title: 'Flutter Dev Android, iOS',
      description: 'Skilled in developing hybrid mobile apps and cross-platform solutions using the Flutter framework.',
      icon: '/assets/icons/flutter-icon.svg',
      technologies: ['Flutter', 'Android', 'iOS', 'Cross-platform']
    }
  ];

  return (
    <section id="expertise" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header - Exact Typography Match */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight">
            My Expertise
          </h2>
        </div>

        {/* Expertise Cards - Exact Layout Match */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 mb-24">
          {expertiseAreas.map((area, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-sm p-8 hover:shadow-lg transition-all duration-300 group border border-gray-100"
            >
              {/* Icon */}
              <div className="w-16 h-16 bg-blue-50 rounded-xl flex items-center justify-center mb-8 group-hover:bg-blue-100 transition-colors duration-300">
                <Image
                  src={area.icon}
                  alt={area.title}
                  width={32}
                  height={32}
                  className="text-blue-600"
                />
              </div>

              {/* Content */}
              <h3 className="text-xl font-semibold text-gray-900 mb-6 leading-tight">
                {area.title}
              </h3>

              <p className="text-gray-600 text-base leading-relaxed mb-8">
                {area.description}
              </p>

              {/* Technologies */}
              <div className="flex flex-wrap gap-3">
                {area.technologies.map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="px-4 py-2 bg-gray-50 text-gray-700 text-sm rounded-lg font-medium"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Quote Section */}
        <div className="text-center">
          <blockquote className="text-xl md:text-2xl font-light text-gray-600 italic max-w-3xl mx-auto">
            "Sometimes the best way to solve a problem is to help others."
          </blockquote>
          <cite className="text-sm text-gray-500 mt-4 block">
            - Uncle Iroh, 'Avatar: The Last Airbender'
          </cite>
        </div>
      </div>
    </section>
  );
};

export default ExpertiseSection;
